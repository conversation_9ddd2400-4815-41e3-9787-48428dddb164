{"name": "server", "scripts": {"dev": "NODE_ENV=development tsx index.ts"}, "type": "module", "dependencies": {"@supabase/supabase-js": "^2.39.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "@types/node": "^22.10.1", "@types/papaparse": "^5.3.14", "@types/uuid": "^9.0.7", "cors": "^2.8.5", "express": "^4.21.1", "multer": "^1.4.5-lts.1", "openai": "^4.73.1", "papaparse": "^5.4.1", "pdf-parse": "^1.1.1", "stripe": "^14.0.0", "tesseract.js": "^5.1.1", "tsx": "^4.19.2", "typescript": "^5.7.2", "uuid": "^9.0.1"}}