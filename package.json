{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "npm --prefix client run dev", "dev:server": "NODE_ENV=development tsx server/index.ts", "build": "npm run build:client && npm run build:server", "build:client": "npm --prefix client run build", "build:server": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "build:server-only": "esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.5", "@types/multer": "^2.0.0", "@types/papaparse": "^5.3.16", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "express": "^4.21.2", "express-session": "^1.18.1", "memorystore": "^1.6.7", "multer": "^2.0.1", "nanoid": "^5.1.5", "openai": "^5.9.0", "papaparse": "^5.5.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "stripe": "^18.3.0", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "tsx": "^4.19.1", "typescript": "^5.6.3", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}