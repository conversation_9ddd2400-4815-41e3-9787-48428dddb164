{"name": "client", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.83.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "lucide-react": "^0.453.0", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.23.0", "tailwind-merge": "^2.6.0", "wouter": "^3.7.1"}, "devDependencies": {"@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.24", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "typescript": "^5.5.0", "vite": "^5.2.0"}}