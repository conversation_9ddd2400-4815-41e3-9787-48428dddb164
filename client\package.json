{"name": "client", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.23.0", "tailwind-merge": "^2.6.0", "wouter": "^3.7.1"}, "devDependencies": {"@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "daisyui": "^4.12.24", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "typescript": "^5.5.0", "vite": "^5.2.0"}}